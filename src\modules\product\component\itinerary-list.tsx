'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Edit, Trash2 } from 'lucide-react'
import Image from "next/image"
import { ItineraryItem } from "../template/package-page"

interface ItineraryListProps {
  items: ItineraryItem[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
}

export function ItineraryList({ items, onEdit, onDelete }: ItineraryListProps) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader><CardTitle>Package Itinerary List</CardTitle></CardHeader>
        <CardContent><p>No itinerary items have been added yet.</p></CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader><CardTitle>Package Itinerary List</CardTitle></CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Day</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Image</TableHead>
              <TableHead className="text-right">Options</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.day}.</TableCell>
                <TableCell title={item.title}>
                  <p className="truncate w-32 md:w-40">
                    {item.title}
                  </p>
                </TableCell>
                <TableCell title={item.details}>
                  <p className="text-sm text-gray-600 truncate w-32 md:w-40">
                    {item.details}
                  </p>
                </TableCell>
                <TableCell>
                  {item.image && (
                    <Image src={item.image} alt={item.title} width={64} height={64} className="w-16 h-16 object-cover rounded-md" />
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button onClick={() => onEdit(item.id)} size="sm" variant="outline" className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button onClick={() => onDelete(item.id)} size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}