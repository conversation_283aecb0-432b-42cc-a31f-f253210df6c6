'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { FileUpload } from "./fileupload"
import dynamic from "next/dynamic"
import { useState } from "react"

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

export function PackageDetailsForm() {
  const [overview, setOverview] = useState("")
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="package-name">Package Name(H1)</Label>
              <Input id="package-name" defaultValue="Khopra Ridge Trek" />
            </div>
            <div>
              <Label htmlFor="region">Select Region</Label>
              <Select defaultValue="annapurna">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="annapurna">Annapurna Region</SelectItem>
                  <SelectItem value="everest">Everest Region</SelectItem>
                  <SelectItem value="langtang">Langtang Region</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="accommodation">Accommodation</Label>
              <Input id="accommodation" defaultValue="Hotel, Lodge and Tea house" />
            </div>
            <div>
              <Label htmlFor="trail-type">Trail Type</Label>
              <Input id="trail-type" defaultValue="" />
            </div>
            <div>
              <Label htmlFor="max-altitude">Max Altitude</Label>
              <Input id="max-altitude" defaultValue="4,600 m | 15091 ft" />
            </div>
            <div>
              <Label htmlFor="group-size">Group Size</Label>
              <Input id="group-size" defaultValue="1-25" />
            </div>
            <div>
              <Label htmlFor="best-season">Best Season</Label>
              <Input id="best-season" defaultValue="March-May / August-November" />
            </div>
            <div>
              <Label htmlFor="price">Price</Label>
              <Input id="price" defaultValue="$ 630" />
            </div>
            <div>
              <Label htmlFor="activity-per-day">Activity Per Day</Label>
              <Input id="activity-per-day" defaultValue="6-7 hrs" />
            </div>
            <div>
              <Label htmlFor="grade">Grade</Label>
              <Input id="grade" defaultValue="Moderate" />
            </div>
            <div>
              <FileUpload
                label="Image"
                accept="image/*"
                showPreview={true}
                previewAlt="Package preview"
              />
            </div>
            <div>
              <FileUpload
                label="PDF"
                accept=".pdf"
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="activity">Select Activity</Label>
              <Select defaultValue="trekking">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="trekking">Trekking</SelectItem>
                  <SelectItem value="peak-climbing">Peak Climbing</SelectItem>
                  <SelectItem value="trail-running">Trail Running</SelectItem>
                  <SelectItem value="fastpacking">Fast Packing</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="slug">Slug</Label>
              <Input id="slug" defaultValue="khopra-trek-nepal" />
            </div>
            <div>
              <Label htmlFor="distance">Distance</Label>
              <Input id="distance" defaultValue="55 km" />
            </div>
            <div>
              <Label htmlFor="days-nights">Days And Nights (eg. 3 Nights 4 Days)</Label>
              <Input id="days-nights" defaultValue="6 Nights 7 Days" />
            </div>
            <div>
              <Label htmlFor="meals">Meals Include</Label>
              <Input id="meals" defaultValue="Breakfast, Lunch & Dinner" />
            </div>
            <div>
              <Label htmlFor="discount-price">Discount Price</Label>
              <Input id="discount-price" defaultValue="$ 525" />
            </div>
            <div>
              <Label htmlFor="transportation">Transportation</Label>
              <Input id="transportation" defaultValue="Jeep / bus" />
            </div>
            <div>
              <Label htmlFor="image-alt">Image Alt Tag</Label>
              <Input id="image-alt" defaultValue="trekkers enjoying the view in Khopra ridge and Mt. Dhaulagiri as seen in background" />
            </div>
            <div>
              <FileUpload
                label="Thumbnail"
                accept="image/*"
                showPreview={true}
                previewSrc="/placeholder.svg?height=80&width=120"
                previewAlt="Thumbnail preview"
              />
            </div>
            <div>
              <Label htmlFor="booking-link">Booking link</Label>
              <Input id="booking-link" defaultValue='<button class="wtrvl-checkout_button" id="wetravel_button_widget" data-env="https://ww' />
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Label htmlFor="overview">Overview Description</Label>
          <div className="mt-1">
            <RichTextEditor value={overview} onChange={data => setOverview(data)} />

          </div>
        </div>
      </CardContent>
    </Card>
  )
}
