'use client'

import { useState, useEffect, FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit } from 'lucide-react'
import { ItineraryItem } from "../template/package-page"

interface AddItineraryFormProps {
  editingItem: ItineraryItem | null;
  onAddItinerary: (data: Omit<ItineraryItem, 'id'>) => void;
  onUpdateItinerary: (data: ItineraryItem) => void;
  onCancelEdit: () => void;
}

const initialState = {
    heading: '',
    day: '',
    title: '',
    trekDistance: '',
    flightHours: '',
    drivingHour: '',
    highestAltitude: '',
    trekDuration: '',
    details: '',
};

export function AddItineraryForm({ editingItem, onAddItinerary, onUpdateItinerary, onCancelEdit }: AddItineraryFormProps) {
  const [formData, setFormData] = useState(initialState);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const isEditing = editingItem !== null;

  useEffect(() => {
    if (isEditing) {
      setFormData({
          heading: editingItem.heading,
          day: editingItem.day,
          title: editingItem.title,
          trekDistance: editingItem.trekDistance,
          flightHours: editingItem.flightHours,
          drivingHour: editingItem.drivingHour,
          highestAltitude: editingItem.highestAltitude,
          trekDuration: editingItem.trekDuration,
          details: editingItem.details,
      });
      setImageFile(null); 
    } else {
      setFormData(initialState); // Reset form when not editing
    }
  }, [editingItem, isEditing]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
          setImageFile(e.target.files[0]);
      }
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (isEditing) {
      onUpdateItinerary({ ...editingItem, ...formData, imageFile });
    } else {
      onAddItinerary({ ...formData, imageFile });
    }
    setFormData(initialState);
    setImageFile(null);
    const fileInput = document.getElementById('image') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Package Itinerary' : 'Add Package Itinerary'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div><Label htmlFor="heading">Heading</Label><Input id="heading" value={formData.heading} onChange={handleChange} placeholder="Enter Heading" /></div>
          <div><Label htmlFor="day">Day Number</Label><Input id="day" value={formData.day} onChange={handleChange} placeholder="Enter Day Number" /></div>
          <div><Label htmlFor="title">Title</Label><Input id="title" value={formData.title} onChange={handleChange} placeholder="Enter Title" /></div>
          <div><Label htmlFor="details">Activity Details</Label><Input id="details" value={formData.details} onChange={handleChange} placeholder="Enter Details" /></div>
          <div><Label htmlFor="trekDistance">Trek Distance</Label><Input id="trekDistance" value={formData.trekDistance} onChange={handleChange} placeholder="e.g., 5km" /></div>
          <div><Label htmlFor="flightHours">Flight Hours</Label><Input id="flightHours" value={formData.flightHours} onChange={handleChange} placeholder="e.g., 1.5" /></div>
          <div><Label htmlFor="drivingHour">Driving Hour</Label><Input id="drivingHour" value={formData.drivingHour} onChange={handleChange} placeholder="e.g., 3" /></div>
          <div><Label htmlFor="highestAltitude">Highest Altitude</Label><Input id="highestAltitude" value={formData.highestAltitude} onChange={handleChange} placeholder="e.g., 4000m" /></div>
          <div><Label htmlFor="trekDuration">Trek Duration</Label><Input id="trekDuration" value={formData.trekDuration} onChange={handleChange} placeholder="e.g., 6 hours" /></div>
          <div><Label htmlFor="image">Image</Label><Input id="image" type="file" onChange={handleImageChange} /></div>
          
          <div className="flex gap-2 pt-2">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} className="w-full">
                Cancel
              </Button>
            )}
            <Button type="submit" className="w-full">
              {isEditing ? 'Save Changes' : 'Add Itinerary'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}