'use client'

import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"

export function PackageStatusControls() {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex gap-8">
          <div className="flex items-center space-x-2">
            <Checkbox id="published" defaultChecked />
            <Label htmlFor="published">Published</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="trip-month" defaultChecked />
            <Label htmlFor="trip-month">Trip of the month</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="popular-tours" />
            <Label htmlFor="popular-tours">Popular Tours</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="short-trek" defaultChecked />
            <Label htmlFor="short-trek">Short Trek</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
