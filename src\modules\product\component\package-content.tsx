'use client'
import React, { useState } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { PhotoEntry, PhotoSection } from './package-detail/photo-section'
import { InfoEntry, TripInfoSection } from './package-detail/trip-info-section'
import { HighlightSection } from './package-detail/highlight-section'
import { DescriptionSection } from './package-detail/description-section'
import { ShortItinerarySection } from './package-detail/short-itinerary-section'
import { IncludesSection } from './package-detail/include-section'
import { ExcludesSection } from './package-detail/exclude-section'
import { MapSection } from './package-detail/map-section'
import { VideoSection } from './package-detail/review-video'



interface TripHighlightsContentProps {
    activeHighlight: string
}

export function TripHighlightsContent({ activeHighlight }: TripHighlightsContentProps) {
    const [highTitle, setHighTitle] = useState("8 Highlights of the Khopra Ridge Trek")
    const [highBody, setHighBody] = useState("• Mountain views during the trek...")
    const [descTitle, setDescTitle] = useState("Is the Khopra Ridge trek worth it?")
    const [descBody, setDescBody] = useState("It's no wonder this question...")
    const [shortItineraryItems, setShortItineraryItems] = useState<string[]>(["Day 1: Arrival..."])
    const [shortItineraryTitle, setShortItineraryTitle] = useState("Short Itinerary")

    const [photoTitle, setPhotoTitle] = useState("Photo Gallery")
    const [photos, setPhotos] = useState<PhotoEntry[]>([])
    const [videoTitle, setVideoTitle] = useState("Watch Our Trek Video")
    const [youtubeLinks, setYoutubeLinks] = useState<string[]>([""])
    const [incTitle, setIncTitle] = useState("WHAT'S INCLUDED")
    const [incBody, setIncBody] = useState("• All permits and fees.")
    const [exclTitle, setExclTitle] = useState("WHAT'S EXCLUDED")
    const [exclBody, setExclBody] = useState("• International airfare.")
    const [mapTitle, setMapTitle] = useState("Route Map")
    const [, setMapFile] = useState<File | null>(null)
    const [tripInfoTitle, setTripInfoTitle] = useState("Package Information")
    const [tripInfos, setTripInfos] = useState<InfoEntry[]>([])

    const addPhoto = () => setPhotos(p => [...p, { file: null, caption: "" }])
    const removePhoto = (idx: number) => setPhotos(p => p.filter((_, i) => i !== idx))
    const updatePhotoFile = (idx: number, file: File | null) => setPhotos(p => { const n = [...p]; n[idx].file = file; return n; })
    const updatePhotoCaption = (idx: number, caption: string) => setPhotos(p => { const n = [...p]; n[idx].caption = caption; return n; })

    const addYoutubeLink = () => setYoutubeLinks(links => [...links, ""])
    const removeYoutubeLink = (idx: number) => setYoutubeLinks(links => links.filter((_, i) => i !== idx))
    const updateYoutubeLink = (idx: number, url: string) => {
        setYoutubeLinks(links => {
            const newLinks = [...links]
            newLinks[idx] = url
            return newLinks
        })
    }

    const addItineraryItem = () => setShortItineraryItems(items => [...items, ""])
    const removeItineraryItem = (idx: number) => setShortItineraryItems(items => items.filter((_, i) => i !== idx))
    const updateItineraryItem = (idx: number, text: string) => {
        setShortItineraryItems(items => {
            const newItems = [...items]
            newItems[idx] = text
            return newItems
        })
    }

    const addTripInfo = () => setTripInfos(p => [...p, { title: "", body: "", note: "" }])
    const removeTripInfo = (idx: number) => setTripInfos(p => p.filter((_, i) => i !== idx))
    const updateTripInfo = (idx: number, field: "title" | "body" | "note", value: string) => {
        setTripInfos(p => { const n = [...p]; n[idx] = { ...n[idx], [field]: value }; return n; })
    }

    const renderSection = () => {
        switch (activeHighlight) {
            case 'highlights':
                return <HighlightSection title={highTitle} onTitleChange={setHighTitle} body={highBody} onBodyChange={setHighBody} />
            case 'description':
                return <DescriptionSection title={descTitle} onTitleChange={setDescTitle} body={descBody} onBodyChange={setDescBody} />
            case 'shortItinerary':
                return <ShortItinerarySection
                    title={shortItineraryTitle}
                    onTitleChange={setShortItineraryTitle}
                    items={shortItineraryItems}
                    onAddItem={addItineraryItem}
                    onUpdateItem={updateItineraryItem}
                    onRemoveItem={removeItineraryItem}
                />
            case 'photo':
                return <PhotoSection title={photoTitle} onTitleChange={setPhotoTitle} photos={photos} onAddPhoto={addPhoto} onRemovePhoto={removePhoto} onUpdatePhotoFile={updatePhotoFile} onUpdatePhotoCaption={updatePhotoCaption} />
            case 'video':
                return <VideoSection
                    title={videoTitle}
                    onTitleChange={setVideoTitle}
                    links={youtubeLinks}
                    onAddLink={addYoutubeLink}
                    onUpdateLink={updateYoutubeLink}
                    onRemoveLink={removeYoutubeLink}
                />
            case 'includes':
                return <IncludesSection title={incTitle} onTitleChange={setIncTitle} body={incBody} onBodyChange={setIncBody} />
            case 'excludes':
                return <ExcludesSection title={exclTitle} onTitleChange={setExclTitle} body={exclBody} onBodyChange={setExclBody} />
            case 'map':
                return <MapSection title={mapTitle} onTitleChange={setMapTitle} onFileChange={setMapFile} />
            case 'tripInfo':
                return <TripInfoSection title={tripInfoTitle} onTitleChange={setTripInfoTitle} infos={tripInfos} onAddInfo={addTripInfo} onRemoveInfo={removeTripInfo} onUpdateInfo={updateTripInfo} />
            default:
                return <p className="text-center text-gray-500 py-8">Select a section from the sidebar to begin editing.</p>
        }
    }

    return (
        <Card>
            <CardContent className="space-y-6 pt-6">{renderSection()}</CardContent>
        </Card>
    )
}